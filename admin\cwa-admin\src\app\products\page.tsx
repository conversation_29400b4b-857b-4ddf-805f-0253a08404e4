'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import ProductTable from '@/components/products/ProductTable';
import ProductFilters from '@/components/products/ProductFilters';
import productService, { ProductFilters as IProductFilters } from '@/lib/api/productService';
import { Product } from '@/types/user';

export default function Products() {
  const router = useRouter();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [totalProducts, setTotalProducts] = useState(0);

  // Fetch products with filters
  const fetchProducts = useCallback(async (filters: IProductFilters = {}) => {
    try {
      setLoading(true);
      setError(null);

      const response = await productService.getProducts(filters);

      if (response.status === 'success') {
        setProducts(response.data.products);
        setTotalProducts(response.results);
      } else {
        throw new Error('Failed to fetch products');
      }
    } catch (err: any) {
      console.error('Error fetching products:', err);
      setError(err.message || 'Failed to fetch products');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    try {
      const categoriesList = await productService.getCategories();
      setCategories(categoriesList);
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  }, []);

  // Delete product
  const handleDeleteProduct = async (id: string) => {
    try {
      await productService.deleteProduct(id);
      // Refresh products list
      fetchProducts();
      // Show success message (you can add a toast notification here)
      console.log('Product deleted successfully');
    } catch (err: any) {
      console.error('Error deleting product:', err);
      setError(err.message || 'Failed to delete product');
    }
  };

  // Handle filter changes
  const handleFiltersChange = useCallback((filters: IProductFilters) => {
    fetchProducts(filters);
  }, [fetchProducts]);

  // Initial data fetch
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [fetchProducts, fetchCategories]);

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full max-w-full overflow-x-hidden">
        <div className="space-y-6 p-4 md:p-6">
          {/* Header */}
          <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-start ">
              <div className="min-w-0 flex-1">
                <h2 className="text-xl md:text-2xl font-bold text-gray-900 break-words">
                  Products Management
                </h2>
                <p className="text-gray-600 mt-1 text-sm md:text-base break-words">
                  Manage your product catalog with advanced filtering and search
                </p>
                {totalProducts > 0 && (
                  <p className="text-xs md:text-sm text-gray-500 mt-1">
                    {totalProducts} product{totalProducts !== 1 ? 's' : ''} total
                  </p>
                )}
              </div>
              <div className="flex-shrink-0 w-full lg:w-auto">
                <button 
                  onClick={() => router.push('/products/add')}
                  className="w-full lg:w-auto bg-blue-600 text-white px-4 md:px-6 py-2 md:py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 text-sm md:text-base"
                >
                  <svg className="w-4 h-4 md:w-5 md:h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  <span className="whitespace-nowrap">Add New Product</span>
                </button>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="w-full max-w-full overflow-x-hidden">
            <ProductFilters
              onFiltersChange={handleFiltersChange}
              categories={categories}
              loading={loading}
            />
          </div>

          {/* Error Message */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-red-400 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-red-800 break-words">{error}</span>
              </div>
            </div>
          )}

          {/* Products Table */}
          <div className="w-full max-w-full overflow-x-auto">
            <ProductTable
              products={products}
              loading={loading}
              onDelete={handleDeleteProduct}
            />
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
