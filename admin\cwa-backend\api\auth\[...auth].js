const { ensureConnection } = require("../../config/db");
const {
  register,
  login,
  forgotPassword,
  resetPassword,
  googleAuth,
  googleCallback,
} = require("../../controllers/authController");
const {
  validateRegistration,
  validateLogin,
} = require("../../middleware/authMiddleware");

// Helper function to apply middleware
const applyMiddleware = (middleware, req, res) => {
  return new Promise((resolve, reject) => {
    middleware(req, res, (err) => {
      if (err) reject(err);
      else resolve();
    });
  });
};

module.exports = async (req, res) => {
  try {
    // Ensure database connection
    const connected = await ensureConnection();
    if (!connected) {
      throw new Error('Failed to establish database connection');
    }
    
    // Set CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.setHeader('Access-Control-Allow-Credentials', 'true');
    
    // Handle preflight requests
    if (req.method === 'OPTIONS') {
      return res.status(200).end();
    }
    
    // Parse the URL to get the path segments
    const url = new URL(req.url, `http://${req.headers.host}`);
    const pathSegments = url.pathname.split('/').filter(segment => segment);

    // Debug logging
    console.log('Auth API - Request URL:', req.url);
    console.log('Auth API - Path segments:', pathSegments);

    // For catch-all routes in Vercel, we need to find the segments after 'auth'
    const authIndex = pathSegments.findIndex(segment => segment === 'auth');
    const endpoint = authIndex >= 0 && pathSegments[authIndex + 1] ? pathSegments[authIndex + 1] : null;
    const subEndpoint = authIndex >= 0 && pathSegments[authIndex + 2] ? pathSegments[authIndex + 2] : null;

    console.log('Auth API - Endpoint:', endpoint, 'SubEndpoint:', subEndpoint);

    const method = req.method;

    if (method === 'POST') {
      if (endpoint === 'register') {
        // Apply validation middleware manually
        try {
          await applyMiddleware(validateRegistration, req, res);
          return await register(req, res);
        } catch (validationError) {
          if (res.headersSent) return; // Validation failed
          return res.status(400).json({
            status: "error",
            message: "Validation failed"
          });
        }
      } else if (endpoint === 'login') {
        // Apply validation middleware manually
        try {
          await applyMiddleware(validateLogin, req, res);
          return await login(req, res);
        } catch (validationError) {
          if (res.headersSent) return; // Validation failed
          return res.status(400).json({
            status: "error",
            message: "Validation failed"
          });
        }
      } else if (endpoint === 'forgot-password') {
        return await forgotPassword(req, res);
      } else if (endpoint === 'reset-password') {
        return await resetPassword(req, res);
      }
    } else if (method === 'GET') {
      if (endpoint === 'google') {
        if (subEndpoint === 'callback') {
          // GET /api/auth/google/callback
          return await googleCallback(req, res);
        } else {
          // GET /api/auth/google
          return await googleAuth(req, res);
        }
      }
    }
    
    return res.status(404).json({
      status: "error",
      message: "Endpoint not found"
    });
    
  } catch (error) {
    console.error("Auth API error:", error);
    return res.status(500).json({
      status: "error",
      message: "Database connection failed",
      details: error.message
    });
  }
};
