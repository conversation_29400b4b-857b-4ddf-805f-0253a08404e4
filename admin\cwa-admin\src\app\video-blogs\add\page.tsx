'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import VideoBlogForm from '@/components/video-blogs/VideoBlogForm';
import videoBlogService from '@/lib/api/videoBlogService';

export default function AddVideoBlogPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (data: any) => {
    try {
      setLoading(true);
      
      const response = await videoBlogService.createVideoBlog(data);
      
      if (response.status === 'success') {
        // Show success message
        alert('Video blog created successfully!');
        
        // Redirect to video blogs list
        router.push('/video-blogs');
      } else {
        throw new Error(response.message || 'Failed to create video blog');
      }
    } catch (error: any) {
      console.error('Error creating video blog:', error);
      alert('Failed to create video blog: ' + (error.message || 'Unknown error'));
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/video-blogs');
  };

  return (
    <DashboardLayout>
      <div className="min-h-screen w-full max-w-full overflow-x-hidden">
        <div className="space-y-6 p-4 md:p-6">
          {/* Header */}
          <div className="bg-white p-4 md:p-6 rounded-lg shadow-sm">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center lg:justify-between">
              <div className="min-w-0 flex-1">
                <h2 className="text-xl md:text-2xl font-bold text-gray-900 break-words">
                  Add New Video Blog
                </h2>
                <p className="text-gray-600 mt-1 text-sm md:text-base break-words">
                  Create a new video blog entry with YouTube or direct video URL
                </p>
              </div>
            </div>
          </div>

          {/* Form */}
          <VideoBlogForm
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={loading}
          />
        </div>
      </div>
    </DashboardLayout>
  );
}
