import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Define protected routes
  const protectedRoutes = ['/dashboard', '/users', '/products', '/video-blogs', '/orders', '/settings'];
  const authRoutes = ['/auth/login'];

  // Check if the current path is protected
  const isProtectedRoute = protectedRoutes.some(route => pathname.startsWith(route));
  const isAuthRoute = authRoutes.some(route => pathname.startsWith(route));

  // Get token from cookies or headers (we'll use localStorage on client side)
  // For now, we'll let the client-side auth context handle the protection
  // This middleware can be enhanced later for server-side token validation

  if (isProtectedRoute) {
    // Allow the request to proceed - client-side auth will handle redirection
    return NextResponse.next();
  }

  if (isAuthRoute) {
    // Allow access to auth routes
    return NextResponse.next();
  }

  // For root path, redirect to dashboard
  if (pathname === '/') {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
