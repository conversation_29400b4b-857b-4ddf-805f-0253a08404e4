const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const User = require('../models/User');
const { clearCache } = require('../config/redis');

exports.getUser = async (req, res) => {
  try {
    const id = req.params.id;
    const user = await User.findById(id);
    res.json(user);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: error.message });
  }
};

exports.getUsers = async (req, res) => {
  try {
    const users = await User.find();
    res.json(users);
  } catch (error) {
    console.error(error);
    res.status(500).json({ error: error.message });
  }
};

exports.updateUser = async (req, res) => {
    try {
        const id = req.params.id;
        const user = await User.findByIdAndUpdate(id, req.body, { new: true });

        // Invalidate both the specific user cache and the all users cache
        await Promise.all([
            clearCache(`api:/api/users/${id}`),
            clearCache('api:/api/users*')
        ]);

        res.json(user);
    } catch (error) {
        console.error(error);
        res.status(500).json({ error: error.message });
    }
}

exports.deleteUser = async (req, res) => {
    try {
        const id = req.params.id;
        const user = await User.findByIdAndDelete(id);
        if (!user) {
            return res.status(404).json({ error: 'User not found' });
        }

        // Invalidate both the specific user cache and the all users cache
        await Promise.all([
            clearCache(`api:/api/users/${id}`),
            clearCache('api:/api/users*')
        ]);

        res.json({ message: 'User deleted' });

    } catch (error) {
        console.error(error);
        res.status(500).json({ error: error.message });
    }
}


