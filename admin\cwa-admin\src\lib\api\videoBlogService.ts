import { VideoBlog, VideoBlogsResponse, VideoBlogResponse, VideoBlogFilters } from '@/types/user';

export interface VideoBlogFormData {
  title: string;
  description: string;
  videoUrl: string;
  thumbnailUrl: string;
  youtubeUrl?: string;
  youtubeVideoId?: string;
  category: string;
  tags: string[];
  duration?: number;
  isActive?: boolean;
}

class VideoBlogService {
  private baseURL = '/api';

  /**
   * Get all video blogs with optional filters
   */
  async getVideoBlogs(filters: VideoBlogFilters = {}): Promise<VideoBlogsResponse> {
    try {
      const params = new URLSearchParams();
      
      // Add filters to query params
      if (filters.search) {
        params.append('search', filters.search);
      }
      if (filters.category && filters.category !== 'all') {
        params.append('category', filters.category);
      }
      if (filters.tag) {
        params.append('tag', filters.tag);
      }
      if (filters.page) {
        params.append('page', filters.page.toString());
      }
      if (filters.limit) {
        params.append('limit', filters.limit.toString());
      }

      const queryString = params.toString();
      const url = `${this.baseURL}/video-blogs${queryString ? `?${queryString}` : ''}`;
      
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(url, { headers });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching video blogs:', error);
      throw error;
    }
  }

  /**
   * Get a single video blog by ID
   */
  async getVideoBlog(id: string): Promise<VideoBlogResponse> {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${this.baseURL}/video-blogs/${id}`, { headers });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to fetch video blog' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching video blog:', error);
      throw error;
    }
  }

  /**
   * Create a new video blog
   */
  async createVideoBlog(data: VideoBlogFormData): Promise<VideoBlogResponse> {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${this.baseURL}/video-blogs`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to create video blog' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating video blog:', error);
      throw error;
    }
  }

  /**
   * Update a video blog
   */
  async updateVideoBlog(id: string, data: Partial<VideoBlogFormData>): Promise<VideoBlogResponse> {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {
        method: 'PATCH',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to update video blog' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating video blog:', error);
      throw error;
    }
  }

  /**
   * Delete a video blog
   */
  async deleteVideoBlog(id: string): Promise<{ status: string; message: string }> {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null;
      const headers: HeadersInit = {
        'Content-Type': 'application/json',
      };
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      const response = await fetch(`${this.baseURL}/video-blogs/${id}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ message: 'Failed to delete video blog' }));
        throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
      }

      // Handle 204 No Content response
      if (response.status === 204) {
        return { status: 'success', message: 'Video blog deleted successfully' };
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error deleting video blog:', error);
      throw error;
    }
  }

  /**
   * Get video blog categories
   */
  async getCategories(): Promise<string[]> {
    try {
      const response = await this.getVideoBlogs();
      const categories = [...new Set(response.data.videoBlogs.map(blog => blog.category))];
      return categories.filter(Boolean);
    } catch (error) {
      console.error('Error fetching categories:', error);
      return ['General', 'Technology', 'Education', 'Entertainment', 'Business', 'Health', 'Sports'];
    }
  }

  /**
   * Get all unique tags
   */
  async getTags(): Promise<string[]> {
    try {
      const response = await this.getVideoBlogs();
      const allTags = response.data.videoBlogs.flatMap(blog => blog.tags);
      const uniqueTags = [...new Set(allTags)];
      return uniqueTags.filter(Boolean);
    } catch (error) {
      console.error('Error fetching tags:', error);
      return [];
    }
  }

  /**
   * Extract YouTube video ID from URL
   */
  extractYouTubeVideoId(url: string): string | null {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
    const match = url.match(regex);
    return match ? match[1] : null;
  }

  /**
   * Validate YouTube URL
   */
  isValidYouTubeUrl(url: string): boolean {
    const regex = /^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/.+/;
    return regex.test(url);
  }
}

export default new VideoBlogService();
